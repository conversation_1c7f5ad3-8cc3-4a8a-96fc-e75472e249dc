{"ast": null, "code": "var _s = $RefreshSig$(),\n  _s2 = $RefreshSig$(),\n  _s3 = $RefreshSig$(),\n  _s4 = $RefreshSig$();\nimport { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';\nimport { ApiService } from '../services/apiService';\nimport toast from 'react-hot-toast';\n\n// 查询键\nexport const BALANCE_QUERY_KEY = 'balance';\n\n/**\n * 获取余额的Hook\n */\nexport const useBalance = (token, enabled = true) => {\n  _s();\n  return useQuery({\n    queryKey: [BALANCE_QUERY_KEY, token],\n    queryFn: () => ApiService.getBalance(token),\n    enabled: enabled && ApiService.validateTokenFormat(token),\n    staleTime: 5 * 60 * 1000,\n    // 5分钟内不重新获取\n    gcTime: 10 * 60 * 1000,\n    // 缓存10分钟 (v5中cacheTime改名为gcTime)\n    retry: (failureCount, error) => {\n      // 对于认证错误，不重试\n      if ((error === null || error === void 0 ? void 0 : error.statusCode) === 401 || (error === null || error === void 0 ? void 0 : error.statusCode) === 403) {\n        return false;\n      }\n      // 最多重试2次\n      return failureCount < 2;\n    }\n  });\n};\n\n/**\n * 手动刷新余额的Mutation\n */\n_s(useBalance, \"4ZpngI1uv+Uo3WQHEZmTQ5FNM+k=\", false, function () {\n  return [useQuery];\n});\nexport const useRefreshBalance = () => {\n  _s2();\n  const queryClient = useQueryClient();\n  return useMutation({\n    mutationFn: token => ApiService.getBalance(token),\n    onSuccess: (data, token) => {\n      // 更新缓存\n      queryClient.setQueryData([BALANCE_QUERY_KEY, token], data);\n      toast.success('余额刷新成功');\n    },\n    onError: error => {\n      console.error('刷新余额失败:', error);\n      toast.error(error.message || '刷新余额失败');\n    }\n  });\n};\n\n/**\n * 验证token的Mutation\n */\n_s2(useRefreshBalance, \"YK0wzM21ECnncaq5SECwU+/SVdQ=\", false, function () {\n  return [useQueryClient, useMutation];\n});\nexport const useValidateToken = () => {\n  _s3();\n  return useMutation({\n    mutationFn: token => ApiService.validateToken(token),\n    onError: error => {\n      console.error('Token验证失败:', error);\n      // 不显示toast，由组件处理错误显示\n    }\n  });\n};\n\n/**\n * 清除余额缓存\n */\n_s3(useValidateToken, \"wwwtpB20p0aLiHIvSy5P98MwIUg=\", false, function () {\n  return [useMutation];\n});\nexport const useClearBalanceCache = () => {\n  _s4();\n  const queryClient = useQueryClient();\n  return () => {\n    queryClient.removeQueries({\n      queryKey: [BALANCE_QUERY_KEY]\n    });\n    toast.success('缓存已清除');\n  };\n};\n_s4(useClearBalanceCache, \"4R+oYVB2Uc11P7bp1KcuhpkfaTw=\", false, function () {\n  return [useQueryClient];\n});", "map": {"version": 3, "names": ["useQuery", "useMutation", "useQueryClient", "ApiService", "toast", "BALANCE_QUERY_KEY", "useBalance", "token", "enabled", "_s", "query<PERSON><PERSON>", "queryFn", "getBalance", "validateTokenFormat", "staleTime", "gcTime", "retry", "failureCount", "error", "statusCode", "useRefreshBalance", "_s2", "queryClient", "mutationFn", "onSuccess", "data", "setQueryData", "success", "onError", "console", "message", "useValidateToken", "_s3", "validateToken", "useClearBalanceCache", "_s4", "removeQueries"], "sources": ["C:/Users/<USER>/Downloads/augment-balance-main/augment-balance-main/augment-balance-web/frontend/src/hooks/useBalance.ts"], "sourcesContent": ["import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';\nimport { ApiService } from '../services/apiService';\nimport { BalanceData, ApiError } from '../types/api';\nimport toast from 'react-hot-toast';\n\n// 查询键\nexport const BALANCE_QUERY_KEY = 'balance';\n\n/**\n * 获取余额的Hook\n */\nexport const useBalance = (token: string, enabled: boolean = true) => {\n  return useQuery({\n    queryKey: [BALANCE_QUERY_KEY, token],\n    queryFn: () => ApiService.getBalance(token),\n    enabled: enabled && ApiService.validateTokenFormat(token),\n    staleTime: 5 * 60 * 1000, // 5分钟内不重新获取\n    gcTime: 10 * 60 * 1000, // 缓存10分钟 (v5中cacheTime改名为gcTime)\n    retry: (failureCount, error: any) => {\n      // 对于认证错误，不重试\n      if (error?.statusCode === 401 || error?.statusCode === 403) {\n        return false;\n      }\n      // 最多重试2次\n      return failureCount < 2;\n    },\n  });\n};\n\n/**\n * 手动刷新余额的Mutation\n */\nexport const useRefreshBalance = () => {\n  const queryClient = useQueryClient();\n\n  return useMutation({\n    mutationFn: (token: string) => ApiService.getBalance(token),\n    onSuccess: (data: BalanceData, token: string) => {\n      // 更新缓存\n      queryClient.setQueryData([BALANCE_QUERY_KEY, token], data);\n      toast.success('余额刷新成功');\n    },\n    onError: (error: ApiError) => {\n      console.error('刷新余额失败:', error);\n      toast.error(error.message || '刷新余额失败');\n    },\n  });\n};\n\n/**\n * 验证token的Mutation\n */\nexport const useValidateToken = () => {\n  return useMutation({\n    mutationFn: (token: string) => ApiService.validateToken(token),\n    onError: (error: ApiError) => {\n      console.error('Token验证失败:', error);\n      // 不显示toast，由组件处理错误显示\n    },\n  });\n};\n\n/**\n * 清除余额缓存\n */\nexport const useClearBalanceCache = () => {\n  const queryClient = useQueryClient();\n\n  return () => {\n    queryClient.removeQueries({ queryKey: [BALANCE_QUERY_KEY] });\n    toast.success('缓存已清除');\n  };\n};\n"], "mappings": ";;;;AAAA,SAASA,QAAQ,EAAEC,WAAW,EAAEC,cAAc,QAAQ,uBAAuB;AAC7E,SAASC,UAAU,QAAQ,wBAAwB;AAEnD,OAAOC,KAAK,MAAM,iBAAiB;;AAEnC;AACA,OAAO,MAAMC,iBAAiB,GAAG,SAAS;;AAE1C;AACA;AACA;AACA,OAAO,MAAMC,UAAU,GAAGA,CAACC,KAAa,EAAEC,OAAgB,GAAG,IAAI,KAAK;EAAAC,EAAA;EACpE,OAAOT,QAAQ,CAAC;IACdU,QAAQ,EAAE,CAACL,iBAAiB,EAAEE,KAAK,CAAC;IACpCI,OAAO,EAAEA,CAAA,KAAMR,UAAU,CAACS,UAAU,CAACL,KAAK,CAAC;IAC3CC,OAAO,EAAEA,OAAO,IAAIL,UAAU,CAACU,mBAAmB,CAACN,KAAK,CAAC;IACzDO,SAAS,EAAE,CAAC,GAAG,EAAE,GAAG,IAAI;IAAE;IAC1BC,MAAM,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI;IAAE;IACxBC,KAAK,EAAEA,CAACC,YAAY,EAAEC,KAAU,KAAK;MACnC;MACA,IAAI,CAAAA,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEC,UAAU,MAAK,GAAG,IAAI,CAAAD,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEC,UAAU,MAAK,GAAG,EAAE;QAC1D,OAAO,KAAK;MACd;MACA;MACA,OAAOF,YAAY,GAAG,CAAC;IACzB;EACF,CAAC,CAAC;AACJ,CAAC;;AAED;AACA;AACA;AAFAR,EAAA,CAlBaH,UAAU;EAAA,QACdN,QAAQ;AAAA;AAoBjB,OAAO,MAAMoB,iBAAiB,GAAGA,CAAA,KAAM;EAAAC,GAAA;EACrC,MAAMC,WAAW,GAAGpB,cAAc,CAAC,CAAC;EAEpC,OAAOD,WAAW,CAAC;IACjBsB,UAAU,EAAGhB,KAAa,IAAKJ,UAAU,CAACS,UAAU,CAACL,KAAK,CAAC;IAC3DiB,SAAS,EAAEA,CAACC,IAAiB,EAAElB,KAAa,KAAK;MAC/C;MACAe,WAAW,CAACI,YAAY,CAAC,CAACrB,iBAAiB,EAAEE,KAAK,CAAC,EAAEkB,IAAI,CAAC;MAC1DrB,KAAK,CAACuB,OAAO,CAAC,QAAQ,CAAC;IACzB,CAAC;IACDC,OAAO,EAAGV,KAAe,IAAK;MAC5BW,OAAO,CAACX,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/Bd,KAAK,CAACc,KAAK,CAACA,KAAK,CAACY,OAAO,IAAI,QAAQ,CAAC;IACxC;EACF,CAAC,CAAC;AACJ,CAAC;;AAED;AACA;AACA;AAFAT,GAAA,CAjBaD,iBAAiB;EAAA,QACRlB,cAAc,EAE3BD,WAAW;AAAA;AAiBpB,OAAO,MAAM8B,gBAAgB,GAAGA,CAAA,KAAM;EAAAC,GAAA;EACpC,OAAO/B,WAAW,CAAC;IACjBsB,UAAU,EAAGhB,KAAa,IAAKJ,UAAU,CAAC8B,aAAa,CAAC1B,KAAK,CAAC;IAC9DqB,OAAO,EAAGV,KAAe,IAAK;MAC5BW,OAAO,CAACX,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;MAClC;IACF;EACF,CAAC,CAAC;AACJ,CAAC;;AAED;AACA;AACA;AAFAc,GAAA,CAVaD,gBAAgB;EAAA,QACpB9B,WAAW;AAAA;AAYpB,OAAO,MAAMiC,oBAAoB,GAAGA,CAAA,KAAM;EAAAC,GAAA;EACxC,MAAMb,WAAW,GAAGpB,cAAc,CAAC,CAAC;EAEpC,OAAO,MAAM;IACXoB,WAAW,CAACc,aAAa,CAAC;MAAE1B,QAAQ,EAAE,CAACL,iBAAiB;IAAE,CAAC,CAAC;IAC5DD,KAAK,CAACuB,OAAO,CAAC,OAAO,CAAC;EACxB,CAAC;AACH,CAAC;AAACQ,GAAA,CAPWD,oBAAoB;EAAA,QACXhC,cAAc;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}