# Augment Balance Web

一个用于监控Augment Code API余额的Web应用，包含React前端和Node.js后端。

![License](https://img.shields.io/badge/license-MIT-blue.svg)
![Node.js](https://img.shields.io/badge/node.js-18+-green.svg)
![React](https://img.shields.io/badge/react-18+-blue.svg)
![TypeScript](https://img.shields.io/badge/typescript-5+-blue.svg)

## 🌟 功能特性

- 🔋 **实时余额监控** - 快速获取最新的Augment Code API余额
- 🎨 **响应式设计** - 完美支持桌面和移动设备
- ⚡ **快速部署** - 支持Docker一键部署
- 🔒 **安全可靠** - Token仅用于查询，不会存储在服务器
- 💾 **智能缓存** - 减少不必要的API调用
- 🚨 **状态警告** - 余额不足时的视觉提醒
- 🔄 **自动刷新** - 支持手动和自动刷新余额

## 📁 项目结构

```
augment-balance-web/
├── frontend/              # React前端应用
│   ├── src/
│   │   ├── components/    # React组件
│   │   ├── services/      # API服务
│   │   ├── hooks/         # 自定义Hooks
│   │   ├── utils/         # 工具函数
│   │   └── types/         # TypeScript类型定义
│   ├── public/
│   └── package.json
├── backend/               # Node.js后端API
│   ├── src/
│   │   ├── routes/        # API路由
│   │   ├── services/      # 业务逻辑
│   │   ├── middleware/    # 中间件
│   │   └── utils/         # 工具函数
│   └── package.json
├── docker-compose.yml     # Docker部署配置
├── start-dev.sh          # 开发环境启动脚本(Linux/Mac)
├── start-dev.bat         # 开发环境启动脚本(Windows)
├── test-api.js           # API测试脚本
└── README.md
```

## 🛠️ 技术栈

### 前端
- **React 18** - 现代化的用户界面框架
- **TypeScript** - 类型安全的JavaScript
- **Tailwind CSS** - 实用优先的CSS框架
- **React Query** - 强大的数据获取和状态管理
- **Axios** - HTTP客户端
- **Lucide React** - 美观的图标库

### 后端
- **Node.js** - JavaScript运行时
- **Express.js** - Web应用框架
- **TypeScript** - 类型安全的JavaScript
- **CORS** - 跨域资源共享
- **Helmet** - 安全中间件
- **Morgan** - HTTP请求日志

## 🚀 快速开始

### 方式一：自动启动脚本（推荐）

**Windows用户：**
```bash
# 双击运行或在命令行执行
start-dev.bat
```

**Linux/Mac用户：**
```bash
# 给脚本执行权限
chmod +x start-dev.sh

# 运行脚本
./start-dev.sh
```

### 方式二：手动启动

1. **安装依赖**
```bash
# 后端依赖
cd backend && npm install

# 前端依赖
cd frontend && npm install
```

2. **配置环境变量**
```bash
# 复制环境变量模板
cp backend/.env.example backend/.env

# 根据需要修改配置
```

3. **启动开发服务器**
```bash
# 启动后端 (端口3001)
cd backend && npm run dev

# 新开终端，启动前端 (端口3000)
cd frontend && npm start
```

4. **访问应用**
- 前端应用：http://localhost:3000
- 后端API：http://localhost:3001
- 健康检查：http://localhost:3001/api/health

## 🐳 Docker部署

### 开发环境
```bash
# 构建并启动所有服务
docker-compose up -d

# 查看日志
docker-compose logs -f

# 停止服务
docker-compose down
```

### 生产环境
```bash
# 使用生产配置
docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d
```

## 🧪 测试

### API测试
```bash
# 安装测试依赖
npm install axios

# 运行API测试
node test-api.js
```

### 前端测试
```bash
cd frontend
npm test
```

### 后端测试
```bash
cd backend
npm test
```

## 📖 使用指南

### 获取API Token

1. **登录Augment官网** - 访问 [Augment Code官网](https://portal.withorb.com)
2. **进入控制台** - 找到显示额度的控制台页面
3. **点击View usage** - 进入使用详情页面
4. **复制Token** - 从URL中的`token`参数复制值

**注意：** 团队子账号可能没有"View usage"选项，请使用团队主账号获取token。

### 使用Web应用

1. **输入Token** - 在首页输入框中粘贴您的API token
2. **查询余额** - 点击"查询余额"按钮
3. **查看结果** - 系统会显示当前余额和状态
4. **刷新数据** - 可以手动点击刷新按钮更新余额

### 余额状态说明

- 🟢 **充足** - 余额 ≥ 100
- 🟡 **不足** - 余额 10-99
- 🔴 **危险** - 余额 1-9
- ⚫ **耗尽** - 余额 = 0

## 🔧 配置说明

### 后端环境变量

```bash
# 服务器配置
PORT=3001                           # 服务器端口
NODE_ENV=development               # 运行环境

# CORS配置
CORS_ORIGIN=http://localhost:3000  # 允许的前端域名

# API配置
API_TIMEOUT=10000                  # API请求超时时间(毫秒)

# 速率限制
RATE_LIMIT_WINDOW_MS=60000         # 速率限制窗口(毫秒)
RATE_LIMIT_MAX_REQUESTS=100        # 窗口内最大请求数
```

### 前端环境变量

```bash
# API配置
REACT_APP_API_URL=http://localhost:3001  # 后端API地址
```

## 📡 API文档

### 健康检查

**GET** `/api/health`

响应示例：
```json
{
  "success": true,
  "data": {
    "status": "healthy",
    "timestamp": "2024-01-01T00:00:00.000Z",
    "uptime": 3600,
    "environment": "development",
    "version": "1.0.0"
  }
}
```

### 验证Token

**POST** `/api/validate-token`

请求体：
```json
{
  "token": "your_api_token_here"
}
```

响应示例：
```json
{
  "success": true,
  "data": {
    "valid": true,
    "customerId": "customer_123",
    "timestamp": 1640995200000
  }
}
```

### 获取余额

**GET** `/api/balance?token=YOUR_TOKEN`

响应示例：
```json
{
  "success": true,
  "data": {
    "balance": "150.50",
    "customerId": "customer_123",
    "timestamp": 1640995200000,
    "formattedBalance": "150.5"
  }
}
```

### 错误响应

```json
{
  "success": false,
  "error": {
    "message": "Token格式无效",
    "statusCode": 400,
    "code": "INVALID_TOKEN_FORMAT",
    "timestamp": 1640995200000
  }
}
```

## 🚨 故障排除

### 常见问题

**1. 后端启动失败**
- 检查端口3001是否被占用
- 确认Node.js版本 >= 16
- 检查依赖是否正确安装

**2. 前端无法连接后端**
- 确认后端服务正在运行
- 检查CORS配置
- 验证API_URL配置

**3. Token验证失败**
- 确认token格式正确（长度>10）
- 检查token是否过期
- 确认使用的是主账号token

**4. Docker部署问题**
- 检查Docker和Docker Compose版本
- 确认端口未被占用
- 查看容器日志：`docker-compose logs`

### 调试模式

开启详细日志：
```bash
# 后端
NODE_ENV=development npm run dev

# 前端
REACT_APP_DEBUG=true npm start
```

## 🤝 贡献指南

1. Fork本项目
2. 创建特性分支：`git checkout -b feature/amazing-feature`
3. 提交更改：`git commit -m 'Add amazing feature'`
4. 推送分支：`git push origin feature/amazing-feature`
5. 提交Pull Request

## 📞 支持与反馈

- **QQ交流群**：611135619 (AI Code IDE Studio)
- **GitHub Issues**：[提交问题](https://github.com/gacjie/augment-balance/issues)
- **邮箱**：<EMAIL>

## 📄 许可证

本项目采用 [MIT License](LICENSE) 许可证。

## 🙏 致谢

- [Augment Code](https://portal.withorb.com) - 提供API服务
- [React](https://reactjs.org/) - 前端框架
- [Express.js](https://expressjs.com/) - 后端框架
- [Tailwind CSS](https://tailwindcss.com/) - CSS框架

---

**免责声明：** 本工具仅供学习和个人使用，请勿用于商业用途。使用本工具产生的任何问题，开发者不承担责任。
