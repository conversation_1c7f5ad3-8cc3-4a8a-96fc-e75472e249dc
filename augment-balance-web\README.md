# Augment Balance Web

一个用于监控Augment Code API余额的Web应用，包含React前端和Node.js后端。

## 🌟 功能特性

- 🔋 **实时余额监控** - 快速获取最新的Augment Code API余额
- 🎨 **响应式设计** - 完美支持桌面和移动设备
- 🔒 **安全可靠** - Token仅用于查询，不会存储在服务器
- 💾 **智能缓存** - 减少不必要的API调用
- 🚨 **状态警告** - 余额不足时的视觉提醒
- 🔄 **自动刷新** - 支持手动和自动刷新余额

## 📁 项目结构

```
augment-balance-web/
├── frontend/              # React前端应用
│   ├── src/
│   │   ├── components/    # React组件
│   │   ├── services/      # API服务
│   │   ├── hooks/         # 自定义Hooks
│   │   ├── utils/         # 工具函数
│   │   └── types/         # TypeScript类型定义
│   ├── public/
│   └── package.json
├── backend/               # Node.js后端API
│   ├── src/
│   │   ├── routes/        # API路由
│   │   ├── services/      # 业务逻辑
│   │   ├── middleware/    # 中间件
│   │   └── utils/         # 工具函数
│   └── package.json
└── README.md
```

## 🛠️ 技术栈

### 前端
- **React 18** - 现代化的用户界面框架
- **TypeScript** - 类型安全的JavaScript
- **Tailwind CSS** - 实用优先的CSS框架
- **React Query** - 强大的数据获取和状态管理
- **Axios** - HTTP客户端
- **Lucide React** - 美观的图标库

### 后端
- **Node.js** - JavaScript运行时
- **Express.js** - Web应用框架
- **TypeScript** - 类型安全的JavaScript
- **CORS** - 跨域资源共享
- **Helmet** - 安全中间件
- **Morgan** - HTTP请求日志

## 🚀 开发环境启动

### 安装依赖

```bash
# 后端依赖
cd backend && npm install

# 前端依赖
cd frontend && npm install
```

### 配置环境变量

```bash
# 复制环境变量模板
cp backend/.env.example backend/.env

# 根据需要修改配置
```

### 启动开发服务器

```bash
# 启动后端 (端口3001)
cd backend && npm run dev

# 新开终端，启动前端 (端口3000)
cd frontend && npm start
```

### 访问应用

- 前端应用：http://localhost:3000
- 后端API：http://localhost:3001
- 健康检查：http://localhost:3001/api/health

## 📖 使用指南

### 获取API Token

1. **登录Augment官网** - 访问 [Augment Code官网](https://portal.withorb.com)
2. **进入控制台** - 找到显示额度的控制台页面
3. **点击View usage** - 进入使用详情页面
4. **复制Token** - 从URL中的`token`参数复制值

**注意：** 团队子账号可能没有"View usage"选项，请使用团队主账号获取token。

### 使用Web应用

1. **输入Token** - 在首页输入框中粘贴您的API token
2. **查询余额** - 点击"查询余额"按钮
3. **查看结果** - 系统会显示当前余额和状态
4. **刷新数据** - 可以手动点击刷新按钮更新余额

### 余额状态说明

- 🟢 **充足** - 余额 ≥ 100
- 🟡 **不足** - 余额 10-99
- 🔴 **危险** - 余额 1-9
- ⚫ **耗尽** - 余额 = 0

## 📡 API接口

### 健康检查
```
GET /api/health
```

### 验证Token
```
POST /api/validate-token
Body: { "token": "your_token" }
```

### 获取余额
```
GET /api/balance?token=YOUR_TOKEN
```

## 🤝 贡献指南

1. Fork本项目
2. 创建特性分支：`git checkout -b feature/amazing-feature`
3. 提交更改：`git commit -m 'Add amazing feature'`
4. 推送分支：`git push origin feature/amazing-feature`
5. 提交Pull Request

## 📞 支持与反馈

- **QQ交流群**：611135619 (AI Code IDE Studio)
- **GitHub Issues**：[提交问题](https://github.com/gacjie/augment-balance/issues)

## 📄 许可证

MIT License
