{"name": "webpack-merge", "description": "Variant of merge that's useful for webpack configuration", "author": "<PERSON><PERSON> <<EMAIL>>", "version": "6.0.1", "scripts": {"build": "tsc", "format": "prettier . --write --ignore-path .gitignore", "start": "dts watch", "test": "dts test", "prepare": "npm run build"}, "main": "dist/index.js", "typings": "dist/index.d.ts", "files": ["dist"], "dependencies": {"clone-deep": "^4.0.1", "flat": "^5.0.2", "wildcard": "^2.0.1"}, "devDependencies": {"@types/estree": "1.0.2", "@types/flat": "^5.0.3", "dts-cli": "^2.0.3", "husky": "^6.0.0", "prettier": "^3.0.3", "tslib": "^2.6.2", "typescript": "^5.2.2", "webpack": "^5.89.0"}, "repository": {"type": "git", "url": "https://github.com/survivejs/webpack-merge.git"}, "homepage": "https://github.com/survivejs/webpack-merge", "bugs": {"url": "https://github.com/survivejs/webpack-merge/issues"}, "keywords": ["webpack", "merge"], "engines": {"node": ">=18.0.0"}, "jest": {"collectCoverage": true, "collectCoverageFrom": ["dist/*.js"]}, "license": "MIT", "husky": {"hooks": {"pre-commit": "npm run test"}}}