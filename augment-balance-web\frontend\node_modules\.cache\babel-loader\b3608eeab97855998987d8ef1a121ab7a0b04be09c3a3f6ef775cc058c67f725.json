{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\augment-balance-main\\\\augment-balance-main\\\\augment-balance-web\\\\frontend\\\\src\\\\index.tsx\";\nimport React from 'react';\nimport ReactDOM from 'react-dom/client';\nimport { QueryClient, QueryClientProvider } from '@tanstack/react-query';\nimport { Toaster } from 'react-hot-toast';\nimport App from './App';\nimport './index.css';\n\n// 创建React Query客户端\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst queryClient = new QueryClient({\n  defaultOptions: {\n    queries: {\n      retry: 2,\n      staleTime: 5 * 60 * 1000,\n      // 5分钟\n      gcTime: 10 * 60 * 1000,\n      // 10分钟 (v5中cacheTime改名为gcTime)\n      refetchOnWindowFocus: false\n    },\n    mutations: {\n      retry: 1\n    }\n  }\n});\nconst root = ReactDOM.createRoot(document.getElementById('root'));\nroot.render(/*#__PURE__*/_jsxDEV(React.StrictMode, {\n  children: /*#__PURE__*/_jsxDEV(QueryClientProvider, {\n    client: queryClient,\n    children: [/*#__PURE__*/_jsxDEV(App, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 30,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Toaster, {\n      position: \"top-right\",\n      toastOptions: {\n        duration: 4000,\n        style: {\n          background: '#363636',\n          color: '#fff'\n        },\n        success: {\n          duration: 3000,\n          iconTheme: {\n            primary: '#22c55e',\n            secondary: '#fff'\n          }\n        },\n        error: {\n          duration: 5000,\n          iconTheme: {\n            primary: '#ef4444',\n            secondary: '#fff'\n          }\n        }\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 31,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 29,\n    columnNumber: 5\n  }, this)\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 28,\n  columnNumber: 3\n}, this));", "map": {"version": 3, "names": ["React", "ReactDOM", "QueryClient", "QueryClientProvider", "Toaster", "App", "jsxDEV", "_jsxDEV", "queryClient", "defaultOptions", "queries", "retry", "staleTime", "gcTime", "refetchOnWindowFocus", "mutations", "root", "createRoot", "document", "getElementById", "render", "StrictMode", "children", "client", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "position", "toastOptions", "duration", "style", "background", "color", "success", "iconTheme", "primary", "secondary", "error"], "sources": ["C:/Users/<USER>/Downloads/augment-balance-main/augment-balance-main/augment-balance-web/frontend/src/index.tsx"], "sourcesContent": ["import React from 'react';\nimport ReactDOM from 'react-dom/client';\nimport { QueryClient, QueryClientProvider } from '@tanstack/react-query';\nimport { Toaster } from 'react-hot-toast';\nimport App from './App';\nimport './index.css';\n\n// 创建React Query客户端\nconst queryClient = new QueryClient({\n  defaultOptions: {\n    queries: {\n      retry: 2,\n      staleTime: 5 * 60 * 1000, // 5分钟\n      gcTime: 10 * 60 * 1000, // 10分钟 (v5中cacheTime改名为gcTime)\n      refetchOnWindowFocus: false,\n    },\n    mutations: {\n      retry: 1,\n    },\n  },\n});\n\nconst root = ReactDOM.createRoot(\n  document.getElementById('root') as HTMLElement\n);\n\nroot.render(\n  <React.StrictMode>\n    <QueryClientProvider client={queryClient}>\n      <App />\n      <Toaster\n        position=\"top-right\"\n        toastOptions={{\n          duration: 4000,\n          style: {\n            background: '#363636',\n            color: '#fff',\n          },\n          success: {\n            duration: 3000,\n            iconTheme: {\n              primary: '#22c55e',\n              secondary: '#fff',\n            },\n          },\n          error: {\n            duration: 5000,\n            iconTheme: {\n              primary: '#ef4444',\n              secondary: '#fff',\n            },\n          },\n        }}\n      />\n    </QueryClientProvider>\n  </React.StrictMode>\n);\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,QAAQ,MAAM,kBAAkB;AACvC,SAASC,WAAW,EAAEC,mBAAmB,QAAQ,uBAAuB;AACxE,SAASC,OAAO,QAAQ,iBAAiB;AACzC,OAAOC,GAAG,MAAM,OAAO;AACvB,OAAO,aAAa;;AAEpB;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,WAAW,GAAG,IAAIN,WAAW,CAAC;EAClCO,cAAc,EAAE;IACdC,OAAO,EAAE;MACPC,KAAK,EAAE,CAAC;MACRC,SAAS,EAAE,CAAC,GAAG,EAAE,GAAG,IAAI;MAAE;MAC1BC,MAAM,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI;MAAE;MACxBC,oBAAoB,EAAE;IACxB,CAAC;IACDC,SAAS,EAAE;MACTJ,KAAK,EAAE;IACT;EACF;AACF,CAAC,CAAC;AAEF,MAAMK,IAAI,GAAGf,QAAQ,CAACgB,UAAU,CAC9BC,QAAQ,CAACC,cAAc,CAAC,MAAM,CAChC,CAAC;AAEDH,IAAI,CAACI,MAAM,cACTb,OAAA,CAACP,KAAK,CAACqB,UAAU;EAAAC,QAAA,eACff,OAAA,CAACJ,mBAAmB;IAACoB,MAAM,EAAEf,WAAY;IAAAc,QAAA,gBACvCf,OAAA,CAACF,GAAG;MAAAmB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACPpB,OAAA,CAACH,OAAO;MACNwB,QAAQ,EAAC,WAAW;MACpBC,YAAY,EAAE;QACZC,QAAQ,EAAE,IAAI;QACdC,KAAK,EAAE;UACLC,UAAU,EAAE,SAAS;UACrBC,KAAK,EAAE;QACT,CAAC;QACDC,OAAO,EAAE;UACPJ,QAAQ,EAAE,IAAI;UACdK,SAAS,EAAE;YACTC,OAAO,EAAE,SAAS;YAClBC,SAAS,EAAE;UACb;QACF,CAAC;QACDC,KAAK,EAAE;UACLR,QAAQ,EAAE,IAAI;UACdK,SAAS,EAAE;YACTC,OAAO,EAAE,SAAS;YAClBC,SAAS,EAAE;UACb;QACF;MACF;IAAE;MAAAb,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACiB;AAAC;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACN,CACpB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}