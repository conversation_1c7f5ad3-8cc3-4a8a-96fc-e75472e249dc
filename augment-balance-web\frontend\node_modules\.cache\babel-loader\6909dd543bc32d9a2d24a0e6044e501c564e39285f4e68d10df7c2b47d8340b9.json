{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\augment-balance-main\\\\augment-balance-main\\\\augment-balance-web\\\\frontend\\\\src\\\\components\\\\BalanceDashboard.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { RefreshCw, Clock } from 'lucide-react';\nimport { useBalance, useRefreshBalance } from '../hooks/useBalance';\nimport { BalanceCard } from './BalanceCard';\nimport { TokenInput } from './TokenInput';\nimport { LoadingSpinner } from './LoadingSpinner';\nimport { ErrorMessage } from './ErrorMessage';\nimport { validateTokenFormat } from '../utils/balanceUtils';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const BalanceDashboard = () => {\n  _s();\n  const [token, setToken] = useState('');\n  const [shouldFetch, setShouldFetch] = useState(false);\n  const [tokenError, setTokenError] = useState('');\n\n  // 从localStorage恢复token（可选）\n  useEffect(() => {\n    const savedToken = localStorage.getItem('augment_token');\n    if (savedToken) {\n      setToken(savedToken);\n    }\n  }, []);\n\n  // 获取余额数据\n  const {\n    data: balanceData,\n    isLoading,\n    error,\n    isError,\n    refetch\n  } = useBalance(token, shouldFetch);\n\n  // 手动刷新\n  const refreshMutation = useRefreshBalance();\n\n  // 处理token输入\n  const handleTokenSubmit = inputToken => {\n    const validation = validateTokenFormat(inputToken);\n    if (!validation.isValid) {\n      setTokenError(validation.message || '');\n      setShouldFetch(false);\n      return;\n    }\n    setTokenError('');\n    setToken(inputToken);\n    setShouldFetch(true);\n\n    // 可选：保存到localStorage\n    localStorage.setItem('augment_token', inputToken);\n  };\n\n  // 处理刷新\n  const handleRefresh = () => {\n    if (!token) return;\n    if (shouldFetch) {\n      refetch();\n    } else {\n      refreshMutation.mutate(token);\n    }\n  };\n\n  // 清除token\n  const handleClearToken = () => {\n    setToken('');\n    setShouldFetch(false);\n    setTokenError('');\n    localStorage.removeItem('augment_token');\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"card\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-lg font-medium text-gray-900\",\n          children: \"API Token \\u914D\\u7F6E\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 77,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"mt-1 text-sm text-gray-500\",\n          children: \"\\u8BF7\\u8F93\\u5165\\u60A8\\u7684 Augment Code API Token\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 80,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 76,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card-body\",\n        children: /*#__PURE__*/_jsxDEV(TokenInput, {\n          value: token,\n          onSubmit: handleTokenSubmit,\n          onClear: handleClearToken,\n          error: tokenError,\n          disabled: isLoading || refreshMutation.isPending\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 85,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 84,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 75,\n      columnNumber: 7\n    }, this), shouldFetch && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"space-y-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex justify-between items-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-lg font-medium text-gray-900\",\n          children: \"\\u4F59\\u989D\\u4FE1\\u606F\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 100,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: handleRefresh,\n          disabled: isLoading || refreshMutation.isPending,\n          className: \"btn btn-outline\",\n          children: [/*#__PURE__*/_jsxDEV(RefreshCw, {\n            className: `w-4 h-4 mr-2 ${isLoading || refreshMutation.isPending ? 'animate-spin' : ''}`\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 108,\n            columnNumber: 15\n          }, this), \"\\u5237\\u65B0\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 103,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 99,\n        columnNumber: 11\n      }, this), isLoading && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card-body\",\n          children: /*#__PURE__*/_jsxDEV(LoadingSpinner, {\n            message: \"\\u6B63\\u5728\\u83B7\\u53D6\\u4F59\\u989D\\u4FE1\\u606F...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 121,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 120,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 119,\n        columnNumber: 13\n      }, this), isError && error && /*#__PURE__*/_jsxDEV(ErrorMessage, {\n        error: error,\n        onRetry: handleRefresh\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 128,\n        columnNumber: 13\n      }, this), balanceData && !isLoading && /*#__PURE__*/_jsxDEV(BalanceCard, {\n        data: balanceData,\n        onRefresh: handleRefresh,\n        isRefreshing: refreshMutation.isPending\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 136,\n        columnNumber: 13\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 97,\n      columnNumber: 9\n    }, this), !shouldFetch && !isLoading && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"card\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card-body text-center py-12\",\n        children: [/*#__PURE__*/_jsxDEV(Clock, {\n          className: \"w-12 h-12 text-gray-400 mx-auto mb-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 149,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-medium text-gray-900 mb-2\",\n          children: \"\\u7B49\\u5F85\\u914D\\u7F6E\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 150,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-500\",\n          children: \"\\u8BF7\\u8F93\\u5165\\u6709\\u6548\\u7684 API Token \\u5F00\\u59CB\\u67E5\\u8BE2\\u4F59\\u989D\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 153,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 148,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 147,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 73,\n    columnNumber: 5\n  }, this);\n};\n_s(BalanceDashboard, \"BVxfrGMeDWpwFUUbBPzrRIAWVzs=\", false, function () {\n  return [useBalance, useRefreshBalance];\n});\n_c = BalanceDashboard;\nvar _c;\n$RefreshReg$(_c, \"BalanceDashboard\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "RefreshCw", "Clock", "useBalance", "useRefreshBalance", "BalanceCard", "TokenInput", "LoadingSpinner", "ErrorMessage", "validateTokenFormat", "jsxDEV", "_jsxDEV", "BalanceDashboard", "_s", "token", "setToken", "shouldFetch", "setShouldFetch", "tokenError", "setTokenError", "savedToken", "localStorage", "getItem", "data", "balanceData", "isLoading", "error", "isError", "refetch", "refreshMutation", "handleTokenSubmit", "inputToken", "validation", "<PERSON><PERSON><PERSON><PERSON>", "message", "setItem", "handleRefresh", "mutate", "handleClearToken", "removeItem", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "value", "onSubmit", "onClear", "disabled", "isPending", "onClick", "onRetry", "onRefresh", "isRefreshing", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Downloads/augment-balance-main/augment-balance-main/augment-balance-web/frontend/src/components/BalanceDashboard.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { RefreshCw, AlertCircle, CheckCircle, Clock } from 'lucide-react';\nimport { useBalance, useRefreshBalance } from '../hooks/useBalance';\nimport { BalanceCard } from './BalanceCard';\nimport { TokenInput } from './TokenInput';\nimport { LoadingSpinner } from './LoadingSpinner';\nimport { ErrorMessage } from './ErrorMessage';\nimport { validateTokenFormat } from '../utils/balanceUtils';\n\nexport const BalanceDashboard: React.FC = () => {\n  const [token, setToken] = useState('');\n  const [shouldFetch, setShouldFetch] = useState(false);\n  const [tokenError, setTokenError] = useState<string>('');\n\n  // 从localStorage恢复token（可选）\n  useEffect(() => {\n    const savedToken = localStorage.getItem('augment_token');\n    if (savedToken) {\n      setToken(savedToken);\n    }\n  }, []);\n\n  // 获取余额数据\n  const {\n    data: balanceData,\n    isLoading,\n    error,\n    isError,\n    refetch\n  } = useBalance(token, shouldFetch);\n\n  // 手动刷新\n  const refreshMutation = useRefreshBalance();\n\n  // 处理token输入\n  const handleTokenSubmit = (inputToken: string) => {\n    const validation = validateTokenFormat(inputToken);\n    \n    if (!validation.isValid) {\n      setTokenError(validation.message || '');\n      setShouldFetch(false);\n      return;\n    }\n\n    setTokenError('');\n    setToken(inputToken);\n    setShouldFetch(true);\n    \n    // 可选：保存到localStorage\n    localStorage.setItem('augment_token', inputToken);\n  };\n\n  // 处理刷新\n  const handleRefresh = () => {\n    if (!token) return;\n    \n    if (shouldFetch) {\n      refetch();\n    } else {\n      refreshMutation.mutate(token);\n    }\n  };\n\n  // 清除token\n  const handleClearToken = () => {\n    setToken('');\n    setShouldFetch(false);\n    setTokenError('');\n    localStorage.removeItem('augment_token');\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Token输入区域 */}\n      <div className=\"card\">\n        <div className=\"card-header\">\n          <h2 className=\"text-lg font-medium text-gray-900\">\n            API Token 配置\n          </h2>\n          <p className=\"mt-1 text-sm text-gray-500\">\n            请输入您的 Augment Code API Token\n          </p>\n        </div>\n        <div className=\"card-body\">\n          <TokenInput\n            value={token}\n            onSubmit={handleTokenSubmit}\n            onClear={handleClearToken}\n            error={tokenError}\n            disabled={isLoading || refreshMutation.isPending}\n          />\n        </div>\n      </div>\n\n      {/* 余额显示区域 */}\n      {shouldFetch && (\n        <div className=\"space-y-4\">\n          {/* 操作按钮 */}\n          <div className=\"flex justify-between items-center\">\n            <h2 className=\"text-lg font-medium text-gray-900\">\n              余额信息\n            </h2>\n            <button\n              onClick={handleRefresh}\n              disabled={isLoading || refreshMutation.isPending}\n              className=\"btn btn-outline\"\n            >\n              <RefreshCw\n                className={`w-4 h-4 mr-2 ${\n                  (isLoading || refreshMutation.isPending) ? 'animate-spin' : ''\n                }`}\n              />\n              刷新\n            </button>\n          </div>\n\n          {/* 加载状态 */}\n          {isLoading && (\n            <div className=\"card\">\n              <div className=\"card-body\">\n                <LoadingSpinner message=\"正在获取余额信息...\" />\n              </div>\n            </div>\n          )}\n\n          {/* 错误状态 */}\n          {isError && error && (\n            <ErrorMessage\n              error={error as ApiError}\n              onRetry={handleRefresh}\n            />\n          )}\n\n          {/* 成功状态 */}\n          {balanceData && !isLoading && (\n            <BalanceCard\n              data={balanceData}\n              onRefresh={handleRefresh}\n              isRefreshing={refreshMutation.isPending}\n            />\n          )}\n        </div>\n      )}\n\n      {/* 状态指示器 */}\n      {!shouldFetch && !isLoading && (\n        <div className=\"card\">\n          <div className=\"card-body text-center py-12\">\n            <Clock className=\"w-12 h-12 text-gray-400 mx-auto mb-4\" />\n            <h3 className=\"text-lg font-medium text-gray-900 mb-2\">\n              等待配置\n            </h3>\n            <p className=\"text-gray-500\">\n              请输入有效的 API Token 开始查询余额\n            </p>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,SAAS,EAA4BC,KAAK,QAAQ,cAAc;AACzE,SAASC,UAAU,EAAEC,iBAAiB,QAAQ,qBAAqB;AACnE,SAASC,WAAW,QAAQ,eAAe;AAC3C,SAASC,UAAU,QAAQ,cAAc;AACzC,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,mBAAmB,QAAQ,uBAAuB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE5D,OAAO,MAAMC,gBAA0B,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC9C,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGhB,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACiB,WAAW,EAAEC,cAAc,CAAC,GAAGlB,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACmB,UAAU,EAAEC,aAAa,CAAC,GAAGpB,QAAQ,CAAS,EAAE,CAAC;;EAExD;EACAC,SAAS,CAAC,MAAM;IACd,MAAMoB,UAAU,GAAGC,YAAY,CAACC,OAAO,CAAC,eAAe,CAAC;IACxD,IAAIF,UAAU,EAAE;MACdL,QAAQ,CAACK,UAAU,CAAC;IACtB;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAM;IACJG,IAAI,EAAEC,WAAW;IACjBC,SAAS;IACTC,KAAK;IACLC,OAAO;IACPC;EACF,CAAC,GAAGzB,UAAU,CAACW,KAAK,EAAEE,WAAW,CAAC;;EAElC;EACA,MAAMa,eAAe,GAAGzB,iBAAiB,CAAC,CAAC;;EAE3C;EACA,MAAM0B,iBAAiB,GAAIC,UAAkB,IAAK;IAChD,MAAMC,UAAU,GAAGvB,mBAAmB,CAACsB,UAAU,CAAC;IAElD,IAAI,CAACC,UAAU,CAACC,OAAO,EAAE;MACvBd,aAAa,CAACa,UAAU,CAACE,OAAO,IAAI,EAAE,CAAC;MACvCjB,cAAc,CAAC,KAAK,CAAC;MACrB;IACF;IAEAE,aAAa,CAAC,EAAE,CAAC;IACjBJ,QAAQ,CAACgB,UAAU,CAAC;IACpBd,cAAc,CAAC,IAAI,CAAC;;IAEpB;IACAI,YAAY,CAACc,OAAO,CAAC,eAAe,EAAEJ,UAAU,CAAC;EACnD,CAAC;;EAED;EACA,MAAMK,aAAa,GAAGA,CAAA,KAAM;IAC1B,IAAI,CAACtB,KAAK,EAAE;IAEZ,IAAIE,WAAW,EAAE;MACfY,OAAO,CAAC,CAAC;IACX,CAAC,MAAM;MACLC,eAAe,CAACQ,MAAM,CAACvB,KAAK,CAAC;IAC/B;EACF,CAAC;;EAED;EACA,MAAMwB,gBAAgB,GAAGA,CAAA,KAAM;IAC7BvB,QAAQ,CAAC,EAAE,CAAC;IACZE,cAAc,CAAC,KAAK,CAAC;IACrBE,aAAa,CAAC,EAAE,CAAC;IACjBE,YAAY,CAACkB,UAAU,CAAC,eAAe,CAAC;EAC1C,CAAC;EAED,oBACE5B,OAAA;IAAK6B,SAAS,EAAC,WAAW;IAAAC,QAAA,gBAExB9B,OAAA;MAAK6B,SAAS,EAAC,MAAM;MAAAC,QAAA,gBACnB9B,OAAA;QAAK6B,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1B9B,OAAA;UAAI6B,SAAS,EAAC,mCAAmC;UAAAC,QAAA,EAAC;QAElD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLlC,OAAA;UAAG6B,SAAS,EAAC,4BAA4B;UAAAC,QAAA,EAAC;QAE1C;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eACNlC,OAAA;QAAK6B,SAAS,EAAC,WAAW;QAAAC,QAAA,eACxB9B,OAAA,CAACL,UAAU;UACTwC,KAAK,EAAEhC,KAAM;UACbiC,QAAQ,EAAEjB,iBAAkB;UAC5BkB,OAAO,EAAEV,gBAAiB;UAC1BZ,KAAK,EAAER,UAAW;UAClB+B,QAAQ,EAAExB,SAAS,IAAII,eAAe,CAACqB;QAAU;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGL7B,WAAW,iBACVL,OAAA;MAAK6B,SAAS,EAAC,WAAW;MAAAC,QAAA,gBAExB9B,OAAA;QAAK6B,SAAS,EAAC,mCAAmC;QAAAC,QAAA,gBAChD9B,OAAA;UAAI6B,SAAS,EAAC,mCAAmC;UAAAC,QAAA,EAAC;QAElD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLlC,OAAA;UACEwC,OAAO,EAAEf,aAAc;UACvBa,QAAQ,EAAExB,SAAS,IAAII,eAAe,CAACqB,SAAU;UACjDV,SAAS,EAAC,iBAAiB;UAAAC,QAAA,gBAE3B9B,OAAA,CAACV,SAAS;YACRuC,SAAS,EAAE,gBACRf,SAAS,IAAII,eAAe,CAACqB,SAAS,GAAI,cAAc,GAAG,EAAE;UAC7D;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,gBAEJ;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,EAGLpB,SAAS,iBACRd,OAAA;QAAK6B,SAAS,EAAC,MAAM;QAAAC,QAAA,eACnB9B,OAAA;UAAK6B,SAAS,EAAC,WAAW;UAAAC,QAAA,eACxB9B,OAAA,CAACJ,cAAc;YAAC2B,OAAO,EAAC;UAAa;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAGAlB,OAAO,IAAID,KAAK,iBACff,OAAA,CAACH,YAAY;QACXkB,KAAK,EAAEA,KAAkB;QACzB0B,OAAO,EAAEhB;MAAc;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxB,CACF,EAGArB,WAAW,IAAI,CAACC,SAAS,iBACxBd,OAAA,CAACN,WAAW;QACVkB,IAAI,EAAEC,WAAY;QAClB6B,SAAS,EAAEjB,aAAc;QACzBkB,YAAY,EAAEzB,eAAe,CAACqB;MAAU;QAAAR,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzC,CACF;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CACN,EAGA,CAAC7B,WAAW,IAAI,CAACS,SAAS,iBACzBd,OAAA;MAAK6B,SAAS,EAAC,MAAM;MAAAC,QAAA,eACnB9B,OAAA;QAAK6B,SAAS,EAAC,6BAA6B;QAAAC,QAAA,gBAC1C9B,OAAA,CAACT,KAAK;UAACsC,SAAS,EAAC;QAAsC;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC1DlC,OAAA;UAAI6B,SAAS,EAAC,wCAAwC;UAAAC,QAAA,EAAC;QAEvD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLlC,OAAA;UAAG6B,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAE7B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAAChC,EAAA,CAvJWD,gBAA0B;EAAA,QAoBjCT,UAAU,EAGUC,iBAAiB;AAAA;AAAAmD,EAAA,GAvB9B3C,gBAA0B;AAAA,IAAA2C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}