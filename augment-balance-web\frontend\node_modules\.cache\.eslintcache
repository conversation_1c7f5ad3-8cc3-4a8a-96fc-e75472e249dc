[{"C:\\Users\\<USER>\\Downloads\\augment-balance-main\\augment-balance-main\\augment-balance-web\\frontend\\src\\index.tsx": "1", "C:\\Users\\<USER>\\Downloads\\augment-balance-main\\augment-balance-main\\augment-balance-web\\frontend\\src\\App.tsx": "2", "C:\\Users\\<USER>\\Downloads\\augment-balance-main\\augment-balance-main\\augment-balance-web\\frontend\\src\\components\\Footer.tsx": "3", "C:\\Users\\<USER>\\Downloads\\augment-balance-main\\augment-balance-main\\augment-balance-web\\frontend\\src\\components\\Header.tsx": "4", "C:\\Users\\<USER>\\Downloads\\augment-balance-main\\augment-balance-main\\augment-balance-web\\frontend\\src\\components\\BalanceDashboard.tsx": "5", "C:\\Users\\<USER>\\Downloads\\augment-balance-main\\augment-balance-main\\augment-balance-web\\frontend\\src\\components\\LoadingSpinner.tsx": "6", "C:\\Users\\<USER>\\Downloads\\augment-balance-main\\augment-balance-main\\augment-balance-web\\frontend\\src\\components\\BalanceCard.tsx": "7", "C:\\Users\\<USER>\\Downloads\\augment-balance-main\\augment-balance-main\\augment-balance-web\\frontend\\src\\components\\TokenInput.tsx": "8", "C:\\Users\\<USER>\\Downloads\\augment-balance-main\\augment-balance-main\\augment-balance-web\\frontend\\src\\components\\ErrorMessage.tsx": "9", "C:\\Users\\<USER>\\Downloads\\augment-balance-main\\augment-balance-main\\augment-balance-web\\frontend\\src\\utils\\balanceUtils.ts": "10", "C:\\Users\\<USER>\\Downloads\\augment-balance-main\\augment-balance-main\\augment-balance-web\\frontend\\src\\hooks\\useBalance.ts": "11", "C:\\Users\\<USER>\\Downloads\\augment-balance-main\\augment-balance-main\\augment-balance-web\\frontend\\src\\types\\api.ts": "12", "C:\\Users\\<USER>\\Downloads\\augment-balance-main\\augment-balance-main\\augment-balance-web\\frontend\\src\\services\\apiService.ts": "13"}, {"size": 1310, "mtime": 1754715554222, "results": "14", "hashOfConfig": "15"}, {"size": 4442, "mtime": 1754715611782, "results": "16", "hashOfConfig": "15"}, {"size": 1952, "mtime": 1754715838895, "results": "17", "hashOfConfig": "15"}, {"size": 1746, "mtime": 1754715825822, "results": "18", "hashOfConfig": "15"}, {"size": 4509, "mtime": 1754715720954, "results": "19", "hashOfConfig": "15"}, {"size": 790, "mtime": 1754715776843, "results": "20", "hashOfConfig": "15"}, {"size": 3595, "mtime": 1754715740084, "results": "21", "hashOfConfig": "15"}, {"size": 3240, "mtime": 1754715763196, "results": "22", "hashOfConfig": "15"}, {"size": 3000, "mtime": 1754715812131, "results": "23", "hashOfConfig": "15"}, {"size": 3419, "mtime": 1754715675422, "results": "24", "hashOfConfig": "15"}, {"size": 2192, "mtime": 1754715655512, "results": "25", "hashOfConfig": "15"}, {"size": 1186, "mtime": 1754715623552, "results": "26", "hashOfConfig": "15"}, {"size": 3254, "mtime": 1754715640103, "results": "27", "hashOfConfig": "15"}, {"filePath": "28", "messages": "29", "suppressedMessages": "30", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "7un19v", {"filePath": "31", "messages": "32", "suppressedMessages": "33", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "34", "messages": "35", "suppressedMessages": "36", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "37", "messages": "38", "suppressedMessages": "39", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "40", "messages": "41", "suppressedMessages": "42", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "43", "messages": "44", "suppressedMessages": "45", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\Users\\<USER>\\Downloads\\augment-balance-main\\augment-balance-main\\augment-balance-web\\frontend\\src\\index.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\augment-balance-main\\augment-balance-main\\augment-balance-web\\frontend\\src\\App.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\augment-balance-main\\augment-balance-main\\augment-balance-web\\frontend\\src\\components\\Footer.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\augment-balance-main\\augment-balance-main\\augment-balance-web\\frontend\\src\\components\\Header.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\augment-balance-main\\augment-balance-main\\augment-balance-web\\frontend\\src\\components\\BalanceDashboard.tsx", ["67", "68"], [], "C:\\Users\\<USER>\\Downloads\\augment-balance-main\\augment-balance-main\\augment-balance-web\\frontend\\src\\components\\LoadingSpinner.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\augment-balance-main\\augment-balance-main\\augment-balance-web\\frontend\\src\\components\\BalanceCard.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\augment-balance-main\\augment-balance-main\\augment-balance-web\\frontend\\src\\components\\TokenInput.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\augment-balance-main\\augment-balance-main\\augment-balance-web\\frontend\\src\\components\\ErrorMessage.tsx", ["69"], [], "C:\\Users\\<USER>\\Downloads\\augment-balance-main\\augment-balance-main\\augment-balance-web\\frontend\\src\\utils\\balanceUtils.ts", [], [], "C:\\Users\\<USER>\\Downloads\\augment-balance-main\\augment-balance-main\\augment-balance-web\\frontend\\src\\hooks\\useBalance.ts", [], [], "C:\\Users\\<USER>\\Downloads\\augment-balance-main\\augment-balance-main\\augment-balance-web\\frontend\\src\\types\\api.ts", [], [], "C:\\Users\\<USER>\\Downloads\\augment-balance-main\\augment-balance-main\\augment-balance-web\\frontend\\src\\services\\apiService.ts", ["70", "71", "72"], [], {"ruleId": "73", "severity": 1, "message": "74", "line": 2, "column": 21, "nodeType": "75", "messageId": "76", "endLine": 2, "endColumn": 32}, {"ruleId": "73", "severity": 1, "message": "77", "line": 2, "column": 34, "nodeType": "75", "messageId": "76", "endLine": 2, "endColumn": 45}, {"ruleId": "73", "severity": 1, "message": "78", "line": 2, "column": 34, "nodeType": "75", "messageId": "76", "endLine": 2, "endColumn": 38}, {"ruleId": "79", "severity": 1, "message": "80", "line": 59, "column": 7, "nodeType": "81", "messageId": "82", "endLine": 63, "endColumn": 9}, {"ruleId": "79", "severity": 1, "message": "80", "line": 86, "column": 7, "nodeType": "81", "messageId": "82", "endLine": 90, "endColumn": 9}, {"ruleId": "79", "severity": 1, "message": "80", "line": 110, "column": 7, "nodeType": "81", "messageId": "82", "endLine": 114, "endColumn": 9}, "@typescript-eslint/no-unused-vars", "'AlertCircle' is defined but never used.", "Identifier", "unusedVar", "'CheckCircle' is defined but never used.", "'Wifi' is defined but never used.", "no-throw-literal", "Expected an error object to be thrown.", "ThrowStatement", "object"]