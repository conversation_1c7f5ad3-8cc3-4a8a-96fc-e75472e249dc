import React, { useState, useEffect } from 'react';
import { RefreshCw, AlertCircle, CheckCircle, Clock } from 'lucide-react';
import { useBalance, useRefreshBalance } from '../hooks/useBalance';
import { BalanceCard } from './BalanceCard';
import { TokenInput } from './TokenInput';
import { LoadingSpinner } from './LoadingSpinner';
import { ErrorMessage } from './ErrorMessage';
import { validateTokenFormat } from '../utils/balanceUtils';
import { ApiError } from '../types/api';

export const BalanceDashboard: React.FC = () => {
  const [token, setToken] = useState('');
  const [shouldFetch, setShouldFetch] = useState(false);
  const [tokenError, setTokenError] = useState<string>('');

  // 从localStorage恢复token（可选）
  useEffect(() => {
    const savedToken = localStorage.getItem('augment_token');
    if (savedToken) {
      setToken(savedToken);
    }
  }, []);

  // 获取余额数据
  const {
    data: balanceData,
    isLoading,
    error,
    isError,
    refetch
  } = useBalance(token, shouldFetch);

  // 手动刷新
  const refreshMutation = useRefreshBalance();

  // 处理token输入
  const handleTokenSubmit = (inputToken: string) => {
    const validation = validateTokenFormat(inputToken);
    
    if (!validation.isValid) {
      setTokenError(validation.message || '');
      setShouldFetch(false);
      return;
    }

    setTokenError('');
    setToken(inputToken);
    setShouldFetch(true);
    
    // 可选：保存到localStorage
    localStorage.setItem('augment_token', inputToken);
  };

  // 处理刷新
  const handleRefresh = () => {
    if (!token) return;
    
    if (shouldFetch) {
      refetch();
    } else {
      refreshMutation.mutate(token);
    }
  };

  // 清除token
  const handleClearToken = () => {
    setToken('');
    setShouldFetch(false);
    setTokenError('');
    localStorage.removeItem('augment_token');
  };

  return (
    <div className="space-y-6">
      {/* Token输入区域 */}
      <div className="card">
        <div className="card-header">
          <h2 className="text-lg font-medium text-gray-900">
            API Token 配置
          </h2>
          <p className="mt-1 text-sm text-gray-500">
            请输入您的 Augment Code API Token
          </p>
        </div>
        <div className="card-body">
          <TokenInput
            value={token}
            onSubmit={handleTokenSubmit}
            onClear={handleClearToken}
            error={tokenError}
            disabled={isLoading || refreshMutation.isPending}
          />
        </div>
      </div>

      {/* 余额显示区域 */}
      {shouldFetch && (
        <div className="space-y-4">
          {/* 操作按钮 */}
          <div className="flex justify-between items-center">
            <h2 className="text-lg font-medium text-gray-900">
              余额信息
            </h2>
            <button
              onClick={handleRefresh}
              disabled={isLoading || refreshMutation.isPending}
              className="btn btn-outline"
            >
              <RefreshCw
                className={`w-4 h-4 mr-2 ${
                  (isLoading || refreshMutation.isPending) ? 'animate-spin' : ''
                }`}
              />
              刷新
            </button>
          </div>

          {/* 加载状态 */}
          {isLoading && (
            <div className="card">
              <div className="card-body">
                <LoadingSpinner message="正在获取余额信息..." />
              </div>
            </div>
          )}

          {/* 错误状态 */}
          {isError && error && (
            <ErrorMessage
              error={error as ApiError}
              onRetry={handleRefresh}
            />
          )}

          {/* 成功状态 */}
          {balanceData && !isLoading && (
            <BalanceCard
              data={balanceData}
              onRefresh={handleRefresh}
              isRefreshing={refreshMutation.isPending}
            />
          )}
        </div>
      )}

      {/* 状态指示器 */}
      {!shouldFetch && !isLoading && (
        <div className="card">
          <div className="card-body text-center py-12">
            <Clock className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              等待配置
            </h3>
            <p className="text-gray-500">
              请输入有效的 API Token 开始查询余额
            </p>
          </div>
        </div>
      )}
    </div>
  );
};
