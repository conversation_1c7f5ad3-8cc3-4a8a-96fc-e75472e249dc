import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { ApiService } from '../services/apiService';
import { BalanceData, ApiError } from '../types/api';
import toast from 'react-hot-toast';

// 查询键
export const BALANCE_QUERY_KEY = 'balance';

/**
 * 获取余额的Hook
 */
export const useBalance = (token: string, enabled: boolean = true) => {
  return useQuery({
    queryKey: [BALANCE_QUERY_KEY, token],
    queryFn: () => ApiService.getBalance(token),
    enabled: enabled && ApiService.validateTokenFormat(token),
    staleTime: 5 * 60 * 1000, // 5分钟内不重新获取
    gcTime: 10 * 60 * 1000, // 缓存10分钟 (v5中cacheTime改名为gcTime)
    retry: (failureCount, error: any) => {
      // 对于认证错误，不重试
      if (error?.statusCode === 401 || error?.statusCode === 403) {
        return false;
      }
      // 最多重试2次
      return failureCount < 2;
    },
  });
};

/**
 * 手动刷新余额的Mutation
 */
export const useRefreshBalance = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (token: string) => ApiService.getBalance(token),
    onSuccess: (data: BalanceData, token: string) => {
      // 更新缓存
      queryClient.setQueryData([BALANCE_QUERY_KEY, token], data);
      toast.success('余额刷新成功');
    },
    onError: (error: ApiError) => {
      console.error('刷新余额失败:', error);
      toast.error(error.message || '刷新余额失败');
    },
  });
};

/**
 * 验证token的Mutation
 */
export const useValidateToken = () => {
  return useMutation({
    mutationFn: (token: string) => ApiService.validateToken(token),
    onError: (error: ApiError) => {
      console.error('Token验证失败:', error);
      // 不显示toast，由组件处理错误显示
    },
  });
};

/**
 * 清除余额缓存
 */
export const useClearBalanceCache = () => {
  const queryClient = useQueryClient();

  return () => {
    queryClient.removeQueries({ queryKey: [BALANCE_QUERY_KEY] });
    toast.success('缓存已清除');
  };
};
