{"version": 3, "file": "convert-ast.js", "sourceRoot": "", "sources": ["convert-ast.ts"], "names": [], "mappings": ";;;AAAA,iCAAiC;AACjC,iCAAoC;AAgCpC;;;GAGG;AACH,SAAgB,UAAU,CAAC,UAAyB;IAChD,MAAM,OAAO,GAAe;QACxB,IAAI,EAAE,UAAU;QAChB,MAAM,EAAE,SAAS;QACjB,IAAI,EAAE,EAAE,CAAC,UAAU,CAAC,UAAU;QAC9B,QAAQ,EAAE,EAAE;QACZ,IAAI,EAAO,SAAS;QACpB,IAAI,EAAE,SAAS;KAClB,CAAC;IACF,MAAM,IAAI,GAAc,EAAE,CAAC;IAC3B,IAAI,OAAO,GAAa,OAAO,CAAC;IAEhC,SAAS,eAAe,CAAC,IAAa;QAClC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC;YAClB,IAAI;YACJ,MAAM,EAAE,OAAO;YACf,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,QAAQ,EAAE,EAAE;YACZ,IAAI,EAAE,SAAS;YACf,IAAI,EAAE,SAAS;SAClB,CAAC,CAAC;IACP,CAAC;IACD,MAAM,KAAK,GAAG,EAAE,CAAC;IACjB,OAAO,IAAI,EAAE;QACT,IAAI,OAAO,CAAC,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE;YAC/B,EAAE,CAAC,YAAY,CAAC,OAAO,CAAC,IAAI,EAAE,eAAe,CAAC,CAAC;YAC/C,IAAI,OAAO,CAAC,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE;gBAC/B,OAAO,GAAG,OAAO,CAAC,MAAO,CAAC,CAAC,wCAAwC;aACtE;iBAAM;gBACH,2BAA2B;gBAC3B,MAAM,UAAU,GAAG,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;gBACvC,OAAO,CAAC,IAAI,GAAG,UAAU,CAAC;gBAC1B,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;gBAC3B,IAAI,iBAAU,CAAC,UAAU,CAAC,IAAI,CAAC;oBAC3B,OAAO,GAAG,UAAU,CAAC;gBACzB,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,sEAAsE;aACxF;SACJ;aAAM;YACH,MAAM,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;YACtC,IAAI,KAAK,GAAG,OAAO,CAAC,QAAQ,CAAC,MAAM,EAAE,EAAE,gCAAgC;gBACnE,MAAM,YAAY,GAAG,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;gBAC7C,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;gBAC7B,IAAI,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;gBAC3C,OAAO,QAAQ,CAAC,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE;oBACnC,QAAQ,CAAC,IAAI,GAAG,YAAY,CAAC;oBAC7B,QAAQ,GAAG,QAAQ,CAAC,QAAQ,CAAC,QAAQ,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;iBAC9D;gBACD,QAAQ,CAAC,IAAI,GAAG,QAAQ,CAAC,IAAI,GAAG,YAAY,CAAC;gBAC7C,EAAE,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;gBAC1B,IAAI,iBAAU,CAAC,YAAY,CAAC,IAAI,CAAC;oBAC7B,OAAO,GAAG,YAAY,CAAC,CAAC,qBAAqB;aACpD;iBAAM;gBACH,oBAAoB;gBACpB,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC;oBAClB,MAAM;gBACV,gDAAgD;gBAChD,KAAK,CAAC,GAAG,EAAE,CAAC;gBACZ,OAAO,GAAG,OAAO,CAAC,MAAO,CAAC;aAC7B;SACJ;KACJ;IAED,OAAO;QACH,OAAO;QACP,IAAI;KACP,CAAC;AACN,CAAC;AAlED,gCAkEC"}