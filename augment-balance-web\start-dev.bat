@echo off
chcp 65001 >nul
title Augment Balance Web - 开发环境

echo 🚀 启动 Augment Balance Web 开发环境...
echo.

:: 检查Node.js版本
echo 📋 检查环境...
node -v
if errorlevel 1 (
    echo ❌ 未找到 Node.js，请先安装 Node.js
    pause
    exit /b 1
)

:: 检查是否安装了依赖
if not exist "backend\node_modules" (
    echo 📦 安装后端依赖...
    cd backend
    call npm install
    cd ..
)

if not exist "frontend\node_modules" (
    echo 📦 安装前端依赖...
    cd frontend
    call npm install
    cd ..
)

:: 创建环境变量文件
if not exist "backend\.env" (
    echo ⚙️ 创建后端环境变量文件...
    copy "backend\.env.example" "backend\.env" >nul
)

:: 启动后端服务器
echo 🔧 启动后端服务器 (端口 3001)...
cd backend
start "Augment Balance Backend" cmd /k "npm run dev"
cd ..

:: 等待后端启动
echo ⏳ 等待后端服务器启动...
timeout /t 5 /nobreak >nul

:: 检查后端是否启动成功
curl -s http://localhost:3001/api/health >nul 2>&1
if errorlevel 1 (
    echo ❌ 后端服务器启动失败，请检查日志
    pause
    exit /b 1
) else (
    echo ✅ 后端服务器启动成功
)

:: 启动前端开发服务器
echo 🎨 启动前端开发服务器 (端口 3000)...
cd frontend
start "Augment Balance Frontend" cmd /k "npm start"
cd ..

echo.
echo 🎉 开发环境启动完成！
echo.
echo 📊 服务地址:
echo    前端: http://localhost:3000
echo    后端: http://localhost:3001
echo    健康检查: http://localhost:3001/api/health
echo.
echo 💡 提示: 前端和后端服务器将在新窗口中运行
echo 🛑 关闭对应窗口即可停止服务
echo.
pause
