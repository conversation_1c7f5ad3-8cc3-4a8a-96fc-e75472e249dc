{"ast": null, "code": "'use strict';\n\n/* eslint-disable es/no-symbol -- required for testing */\nvar NATIVE_SYMBOL = require('../internals/symbol-constructor-detection');\nmodule.exports = NATIVE_SYMBOL && !Symbol.sham && typeof Symbol.iterator == 'symbol';", "map": {"version": 3, "names": ["NATIVE_SYMBOL", "require", "module", "exports", "Symbol", "sham", "iterator"], "sources": ["C:/Users/<USER>/Downloads/augment-balance-main/augment-balance-main/augment-balance-web/frontend/node_modules/core-js-pure/internals/use-symbol-as-uid.js"], "sourcesContent": ["'use strict';\n/* eslint-disable es/no-symbol -- required for testing */\nvar NATIVE_SYMBOL = require('../internals/symbol-constructor-detection');\n\nmodule.exports = NATIVE_SYMBOL &&\n  !Symbol.sham &&\n  typeof Symbol.iterator == 'symbol';\n"], "mappings": "AAAA,YAAY;;AACZ;AACA,IAAIA,aAAa,GAAGC,OAAO,CAAC,2CAA2C,CAAC;AAExEC,MAAM,CAACC,OAAO,GAAGH,aAAa,IAC5B,CAACI,MAAM,CAACC,IAAI,IACZ,OAAOD,MAAM,CAACE,QAAQ,IAAI,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}